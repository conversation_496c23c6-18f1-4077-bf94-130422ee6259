<template>
  <div id="data-view">
    <dv-full-screen-container>
      <!-- 上部区域：标题 + 数据概览 -->
      <div class="top-section">
        <top-header />
        <digital-flop />
      </div>

      <!-- 中部区域：主要数据展示 -->
      <div class="middle-section">
        <div class="middle-left">
          <ranking-board />
        </div>
        <div class="middle-center">
          <rose-chart />
        </div>
        <div class="middle-right">
          <scroll-board />
        </div>
      </div>

      <!-- 下部区域：路段卡片 -->
      <div class="bottom-section">
        <cards />
      </div>
    </dv-full-screen-container>
  </div>
</template>

<script>
import topHeader from './topHeader'
import digitalFlop from './digitalFlop'
import rankingBoard from './rankingBoard'
import roseChart from './roseChart'
import waterLevelChart from './waterLevelChart'
import scrollBoard from './scrollBoard'
import cards from './cards'

export default {
  name: 'DataView',
  components: {
    topHeader,
    digitalFlop,
    rankingBoard,
    roseChart,
    waterLevelChart,
    scrollBoard,
    cards
  },
  data() {
    return {}
  },
  methods: {}
}
</script>

<style lang="less">
#data-view {
  width: 100%;
  height: 100%;
  background-color: #030409;
  color: #fff;

  #dv-full-screen-container {
    background-image: url('./img/bg.png');
    background-size: 100% 100%;
    box-shadow: 0 0 3px blue;
    display: flex;
    flex-direction: column;
    height: 100vh;
    padding: 10px;
    box-sizing: border-box;
  }

  // 上部区域：标题 + 数据概览 (20%)
  .top-section {
    height: 20%;
    display: flex;
    flex-direction: column;
    margin-bottom: 15px;
  }

  // 中部区域：主要数据展示 (60%)
  .middle-section {
    height: 60%;
    display: flex;
    gap: 15px;
    margin-bottom: 15px;
  }

  .middle-left {
    width: 30%;
    display: flex;
    flex-direction: column;
  }

  .middle-center {
    width: 40%;
    display: flex;
    flex-direction: column;
  }

  .middle-right {
    width: 30%;
    display: flex;
    flex-direction: column;
  }

  // 下部区域：路段卡片 (20%)
  .bottom-section {
    height: 20%;
    display: flex;
  }
}
</style>
